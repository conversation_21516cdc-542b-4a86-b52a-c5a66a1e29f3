/**
 * Real-Time Data Manager for Amazon Integration
 * Handles data fetching, caching, and memory management for Chrome extension
 */

class RealTimeDataManager {
  constructor() {
    this.isActive = false;
    this.fetchInterval = null;
    this.fetchFrequency = 30000; // 30 seconds default
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5 seconds
    this.dataBuffer = new Map(); // Temporary buffer for batching
    this.maxBufferSize = 100;
    this.lastFetchTime = 0;
    this.errorCount = 0;
    this.maxErrors = 10;
    
    // Data type configurations
    this.dataTypes = {
      salesData: { priority: 1, batchSize: 50 },
      listingsData: { priority: 2, batchSize: 100 },
      adSpendData: { priority: 3, batchSize: 20 },
      analyticsData: { priority: 4, batchSize: 30 }
    };
  }

  /**
   * Start real-time data fetching
   */
  async startRealTimeUpdates() {
    if (this.isActive) {
      if (window.SnapLogger) {
        window.SnapLogger.warn('⚠️ Real-time updates already active');
      }
      return;
    }

    // Skip real-time updates entirely in zero-data mock mode
    if (window.USE_MOCK_ZERO_DATA || (window.MockZeroData && window.MockZeroData.isEnabled())) {
      if (window.SnapLogger) {
        window.SnapLogger.info('🧪 Mock Zero Data enabled: skipping real-time updates');
      }
      return;
    }

    if (window.SnapLogger) {
      window.SnapLogger.info('🚀 Starting real-time Amazon data updates...');
    }
    this.isActive = true;
    this.errorCount = 0;

    // Initial fetch
    await this.fetchAllData();

    // Setup interval using tracked interval
    this.fetchInterval = window.EventCleanupManager.setInterval(
      () => this.fetchAllData(),
      this.fetchFrequency
    );

    if (window.SnapLogger) {
      window.SnapLogger.info(`✅ Real-time updates started (${this.fetchFrequency}ms interval)`);
    }
  }

  /**
   * Stop real-time data fetching
   */
  stopRealTimeUpdates() {
    if (!this.isActive) return;

    if (window.SnapLogger) {
      window.SnapLogger.info('🛑 Stopping real-time updates...');
    }
    this.isActive = false;

    if (this.fetchInterval) {
      clearInterval(this.fetchInterval);
      this.fetchInterval = null;
    }

    // Flush any remaining buffered data
    this.flushDataBuffer();

    if (window.SnapLogger) {
      window.SnapLogger.info('✅ Real-time updates stopped');
    }
  }

  /**
   * Fetch all data types with error handling
   */
  async fetchAllData() {
    if (!this.isActive) return;
    
    const startTime = Date.now();
    console.log('🔄 Fetching real-time Amazon data...');
    
    try {
      // Fetch data in priority order
      const sortedTypes = Object.entries(this.dataTypes)
        .sort(([,a], [,b]) => a.priority - b.priority);
      
      for (const [dataType, config] of sortedTypes) {
        if (!this.isActive) break; // Stop if deactivated during fetch
        
        try {
          await this.fetchDataType(dataType, config);
        } catch (error) {
          console.error(`❌ Failed to fetch ${dataType}:`, error);
          this.handleFetchError(dataType, error);
        }
      }
      
      // Flush buffered data to IndexedDB
      await this.flushDataBuffer();
      
      this.lastFetchTime = Date.now();
      const duration = this.lastFetchTime - startTime;
      console.log(`✅ Data fetch completed in ${duration}ms`);
      
      // Reset error count on successful fetch
      this.errorCount = 0;
      
    } catch (error) {
      console.error('❌ Critical error in fetchAllData:', error);
      this.handleCriticalError(error);
    }
  }

  /**
   * Fetch specific data type from Amazon
   */
  async fetchDataType(dataType, config) {
    // This would be replaced with actual Amazon API calls
    // For now, simulate the fetch
    
    console.log(`📡 Fetching ${dataType}...`);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    // Generate mock data (replace with real Amazon API calls)
    const mockData = this.generateMockData(dataType, config.batchSize);
    
    // Add to buffer for batching
    this.addToBuffer(dataType, mockData);
    
    return mockData;
  }

  /**
   * Add data to buffer for efficient batching
   */
  addToBuffer(dataType, data) {
    if (!this.dataBuffer.has(dataType)) {
      this.dataBuffer.set(dataType, []);
    }
    
    const buffer = this.dataBuffer.get(dataType);
    buffer.push(...(Array.isArray(data) ? data : [data]));
    
    // Auto-flush if buffer gets too large
    if (buffer.length > this.maxBufferSize) {
      this.flushDataType(dataType);
    }
  }

  /**
   * Flush all buffered data to IndexedDB
   */
  async flushDataBuffer() {
    for (const [dataType, buffer] of this.dataBuffer.entries()) {
      if (buffer.length > 0) {
        await this.flushDataType(dataType);
      }
    }
  }

  /**
   * Flush specific data type to IndexedDB
   */
  async flushDataType(dataType) {
    const buffer = this.dataBuffer.get(dataType);
    if (!buffer || buffer.length === 0) return;
    
    try {
      console.log(`💾 Flushing ${buffer.length} ${dataType} records to IndexedDB...`);
      
      // Store each record in IndexedDB
      for (const record of buffer) {
        await window.IndexedDBManager.storeAmazonData(dataType, record);
      }
      
      // Clear buffer
      this.dataBuffer.set(dataType, []);
      
      // Update UI with new data
      this.updateUI(dataType, buffer);
      
    } catch (error) {
      console.error(`❌ Failed to flush ${dataType} to IndexedDB:`, error);
      throw error;
    }
  }

  /**
   * Update UI with new data (memory-efficient)
   */
  updateUI(dataType, newData) {
    try {
      // Use the existing real-time update system but with memory management
      if (window.handleRealTimeDataUpdate) {
        // Create update object without deep copying
        const updateData = {};
        updateData[dataType] = newData;
        
        // Call update function
        window.handleRealTimeDataUpdate(updateData);
      }
      
    } catch (error) {
      console.error(`❌ Failed to update UI for ${dataType}:`, error);
    }
  }

  /**
   * Handle fetch errors with exponential backoff
   */
  handleFetchError(dataType, error) {
    this.errorCount++;
    
    console.error(`❌ Fetch error for ${dataType} (${this.errorCount}/${this.maxErrors}):`, error);
    
    if (this.errorCount >= this.maxErrors) {
      console.error('❌ Too many errors, stopping real-time updates');
      this.stopRealTimeUpdates();
      return;
    }
    
    // Implement exponential backoff
    const backoffDelay = this.retryDelay * Math.pow(2, this.errorCount - 1);
    console.log(`⏳ Retrying in ${backoffDelay}ms...`);
    
    setTimeout(() => {
      if (this.isActive) {
        this.fetchDataType(dataType, this.dataTypes[dataType]);
      }
    }, backoffDelay);
  }

  /**
   * Handle critical errors
   */
  handleCriticalError(error) {
    console.error('💥 Critical error in real-time data manager:', error);
    
    // Stop updates and notify user
    this.stopRealTimeUpdates();
    
    // Could dispatch event for UI notification
    window.dispatchEvent(new CustomEvent('realTimeDataError', {
      detail: { error: error.message }
    }));
  }

  /**
   * Generate mock data (replace with real Amazon API integration)
   */
  generateMockData(dataType, count) {
    const data = [];
    
    for (let i = 0; i < count; i++) {
      switch (dataType) {
        case 'salesData':
          data.push({
            id: `sale_${Date.now()}_${i}`,
            asin: `B0${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
            marketplace: ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'][Math.floor(Math.random() * 7)],
            units: Math.floor(Math.random() * 10) + 1,
            royalties: (Math.random() * 50).toFixed(2),
            timestamp: Date.now() - Math.random() * 3600000 // Last hour
          });
          break;
          
        case 'listingsData':
          data.push({
            asin: `B0${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
            title: `Product ${i} - ${Math.random().toString(36).substr(2, 5)}`,
            status: ['live', 'draft', 'review'][Math.floor(Math.random() * 3)],
            lastUpdated: Date.now()
          });
          break;
          
        default:
          data.push({
            id: `${dataType}_${Date.now()}_${i}`,
            value: Math.random() * 100,
            timestamp: Date.now()
          });
      }
    }
    
    return data;
  }

  /**
   * Get manager statistics
   */
  getStats() {
    return {
      isActive: this.isActive,
      lastFetchTime: this.lastFetchTime,
      errorCount: this.errorCount,
      bufferSizes: Object.fromEntries(
        Array.from(this.dataBuffer.entries()).map(([key, value]) => [key, value.length])
      ),
      fetchFrequency: this.fetchFrequency
    };
  }

  /**
   * Update fetch frequency
   */
  setFetchFrequency(frequency) {
    this.fetchFrequency = Math.max(5000, frequency); // Minimum 5 seconds
    
    if (this.isActive) {
      this.stopRealTimeUpdates();
      this.startRealTimeUpdates();
    }
  }
}

// Global instance
window.RealTimeDataManager = new RealTimeDataManager();

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.RealTimeDataManager.stopRealTimeUpdates();
});

// Export for global use
window.startRealTimeUpdates = () => window.RealTimeDataManager.startRealTimeUpdates();
window.stopRealTimeUpdates = () => window.RealTimeDataManager.stopRealTimeUpdates();
window.getRealTimeStats = () => window.RealTimeDataManager.getStats();
