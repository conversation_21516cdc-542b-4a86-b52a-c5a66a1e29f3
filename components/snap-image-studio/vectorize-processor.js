// Snap Vectorize Processor
// Client-side raster-to-vector pipeline:
// 1) Load image to canvas, optional blur
// 2) K-means color quantization
// 3) Region growing + contour tracing
// 4) <PERSON><PERSON><PERSON>–Peucker path simplification
// 5) SVG path assembly with optional background removal

(function(){
  function loadImageToCanvas(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        URL.revokeObjectURL(url);
        resolve({canvas, ctx, width: canvas.width, height: canvas.height});
      };
      img.onerror = (e) => {
        URL.revokeObjectURL(url);
        reject(e);
      };
      img.src = url;
    });
  }

  function gaussianBlur(ctx, width, height, radius) {
    if (radius <= 0) return;
    const off = document.createElement('canvas');
    off.width = width; off.height = height;
    const octx = off.getContext('2d');
    // Copy current canvas to offscreen
    const imgData = ctx.getImageData(0, 0, width, height);
    octx.putImageData(imgData, 0, 0);
    // Draw blurred back to original
    ctx.clearRect(0, 0, width, height);
    ctx.filter = `blur(${radius}px)`;
    ctx.drawImage(off, 0, 0);
    ctx.filter = 'none';
  }

  function getImageData(ctx, width, height) {
    return ctx.getImageData(0,0,width,height);
  }

  function kmeansQuantize(pixels, k, maxIter = 10, palette) {
    // pixels: Uint8ClampedArray RGBA
    const n = pixels.length / 4;
    const data = new Array(n);
    for (let i = 0; i < n; i++) {
      const idx = i * 4;
      data[i] = [pixels[idx], pixels[idx+1], pixels[idx+2]];
    }

    // Initialize centroids by sampling
    const centroids = [];
    if (palette && Array.isArray(palette) && palette.length) {
      for (let i = 0; i < Math.min(k, palette.length); i++) {
        centroids.push(palette[i].slice());
      }
    }
    const used = new Set();
    while (centroids.length < k) {
      const r = Math.floor(Math.random() * n);
      if (!used.has(r)) { centroids.push(data[r].slice()); used.add(r); }
    }

    const labels = new Uint16Array(n);
    for (let iter = 0; iter < maxIter; iter++) {
      // Assign
      for (let i = 0; i < n; i++) {
        let best = 0;
        let bestD = Infinity;
        const [r,g,b] = data[i];
        for (let c = 0; c < k; c++) {
          const cr = centroids[c][0] - r;
          const cg = centroids[c][1] - g;
          const cb = centroids[c][2] - b;
          const d = cr*cr + cg*cg + cb*cb;
          if (d < bestD) { bestD = d; best = c; }
        }
        labels[i] = best;
      }

      // Update
      const sum = new Array(k).fill(0).map(()=>[0,0,0,0]);
      for (let i = 0; i < n; i++) {
        const l = labels[i];
        const [r,g,b] = data[i];
        sum[l][0]+=r; sum[l][1]+=g; sum[l][2]+=b; sum[l][3]++;
      }
      for (let c = 0; c < k; c++) {
        if (sum[c][3] > 0) {
          centroids[c][0] = Math.round(sum[c][0]/sum[c][3]);
          centroids[c][1] = Math.round(sum[c][1]/sum[c][3]);
          centroids[c][2] = Math.round(sum[c][2]/sum[c][3]);
        }
      }
    }

    return { labels, centroids };
  }

  function buildLabelImage(labels, width, height) {
    const img = new Uint16Array(width * height);
    img.set(labels);
    return img;
  }

  function connectedComponents(labelImg, width, height) {
    // BFS per color label to extract regions as arrays of points
    const visited = new Uint8Array(width * height);
    const regions = [];
    const dirs = [[1,0],[-1,0],[0,1],[0,-1]];
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const i = y*width + x;
        if (visited[i]) continue;
        visited[i] = 1;
        const color = labelImg[i];
        const queue = [i];
        const pixels = [i];
        while (queue.length) {
          const q = queue.pop();
          const qx = q % width;
          const qy = (q - qx)/width;
          for (const [dx,dy] of dirs) {
            const nx = qx + dx, ny = qy + dy;
            if (nx<0||ny<0||nx>=width||ny>=height) continue;
            const ni = ny*width + nx;
            if (!visited[ni] && labelImg[ni] === color) {
              visited[ni] = 1;
              queue.push(ni);
              pixels.push(ni);
            }
          }
        }
        regions.push({ color, pixels });
      }
    }
    return regions;
  }

  function traceContour(pixels, width, height) {
    // Generate a simple alpha mask for region and trace outer boundary using Moore-Neighbor tracing
    const mask = new Uint8Array(width*height);
    for (const p of pixels) mask[p]=1;
    // Find a starting boundary pixel
    let start = -1;
    for (const p of pixels) {
      const x = p % width, y = (p-x)/width;
      if (
        (x>0 && !mask[p-1]) || (x<width-1 && !mask[p+1]) ||
        (y>0 && !mask[p-width]) || (y<height-1 && !mask[p+width])
      ) { start = p; break; }
    }
    if (start === -1) return [];

    const contour = [];
    let cx = start % width, cy = (start - cx)/width;
    let px = cx, py = cy; // previous point for direction
    let dir = 0; // 0:E,1:S,2:W,3:N starting search dir
    const neighbors = [
      [1,0],[1,1],[0,1],[-1,1],[-1,0],[-1,-1],[0,-1],[1,-1]
    ];
    let loopGuard = 0;
    do {
      contour.push([cx,cy]);
      // Find next boundary pixel by scanning neighbors
      let found = false;
      for (let k = 0; k < 8; k++) {
        const idx = (dir + k) % 8;
        const [dx,dy] = neighbors[idx];
        const nx = cx + dx, ny = cy + dy;
        if (nx<0||ny<0||nx>=width||ny>=height) continue;
        const ni = ny*width + nx;
        if (mask[ni]) {
          cx = nx; cy = ny;
          dir = (idx + 6) % 8; // turn left relative to movement
          found = true;
          break;
        }
      }
      if (!found) break;
      loopGuard++;
      if (loopGuard > pixels.length*4) break;
    } while (!(cx === start % width && cy === (start - (start%width))/width));
    return contour;
  }

  function rdpSimplify(points, epsilon) {
    if (points.length < 3) return points.slice();
    const dmax = { d: 0, idx: 0 };
    const start = points[0], end = points[points.length-1];
    for (let i = 1; i < points.length-1; i++) {
      const d = perpendicularDistance(points[i], start, end);
      if (d > dmax.d) { dmax.d = d; dmax.idx = i; }
    }
    if (dmax.d > epsilon) {
      const left = rdpSimplify(points.slice(0, dmax.idx+1), epsilon);
      const right = rdpSimplify(points.slice(dmax.idx), epsilon);
      return left.slice(0, -1).concat(right);
    } else {
      return [start, end];
    }
  }

  function perpendicularDistance(p, a, b) {
    const [x,y] = p, [x1,y1] = a, [x2,y2] = b;
    const A = x - x1, B = y - y1, C = x2 - x1, D = y2 - y1;
    const dot = A*C + B*D;
    const len = C*C + D*D;
    let param = -1;
    if (len !== 0) param = dot / len;
    let xx, yy;
    if (param < 0) { xx = x1; yy = y1; }
    else if (param > 1) { xx = x2; yy = y2; }
    else { xx = x1 + param * C; yy = y1 + param * D; }
    const dx = x - xx, dy = y - yy;
    return Math.sqrt(dx*dx + dy*dy);
  }

  function assembleSVG(regions, palette, width, height, options) {
    // Remove background: detect largest region by area if requested
    let filtered = regions;
    if (options.removeBg && regions.length) {
      let maxArea = -1, maxIdx = -1;
      regions.forEach((r, i) => { if (r.pixels.length > maxArea) { maxArea = r.pixels.length; maxIdx = i; } });
      filtered = regions.filter((_, i) => i !== maxIdx);
    }
    // Min area filter
    filtered = filtered.filter(r => r.pixels.length >= (options.minArea || 0));

    const paths = [];
    if (options.tracingMode === 'centerline') {
      // Simple centerline: approximate by stroking the contour with stroke only
      for (const r of filtered) {
        const contour = traceContour(r.pixels, width, height);
        if (!contour.length) continue;
        const eps = Math.max(0.25, 1.5 * (4 - (options.detail || 3)));
        const simple = rdpSimplify(contour, eps);
        const d = pathFromPoints(simple, options.precision);
        const color = palette[r.color];
        const stroke = `rgb(${color[0]},${color[1]},${color[2]})`;
        paths.push({ d, stroke });
      }
    } else {
      for (const r of filtered) {
        const contour = traceContour(r.pixels, width, height);
        if (!contour.length) continue;
        const eps = Math.max(0.25, 1.5 * (4 - (options.detail || 3))); // heuristic clamp
        const simple = rdpSimplify(contour, eps);
        const d = pathFromPoints(simple, options.precision);
        const color = palette[r.color];
        const fill = `rgb(${color[0]},${color[1]},${color[2]})`;
        paths.push({ d, fill });
      }
    }
    // Optional path merge by fill color (basic)
    if (options.mergePaths && options.tracingMode !== 'centerline') {
      const byFill = new Map();
      for (const p of paths) {
        if (!byFill.has(p.fill)) byFill.set(p.fill, []);
        byFill.get(p.fill).push(p.d);
      }
      const merged = [];
      for (const [fill, ds] of byFill.entries()) {
        // Basic concat; a true union would require a boolean library
        merged.push(`<path d="${ds.join(' ')}" fill="${fill}" stroke="none"/>`);
      }
      const open = options.responsiveSvg
        ? `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">`
        : `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
      return `${open}${merged.join('')}</svg>`;
    }
    // Optional no-overlap placeholder (real boolean ops require a geometry lib)
    if (options.noOverlap && options.tracingMode !== 'centerline') {
      // Draw order will determine overlap; advanced ops can replace this.
    }

    const open = options.responsiveSvg
      ? `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">`
      : `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
    if (options.tracingMode === 'outline') {
      const plain = paths.map(p => `<path d="${p.d}" fill="none" stroke="${p.fill}" stroke-width="1"/>`).join('');
      return `${open}${plain}</svg>`;
    }
    if (options.tracingMode === 'centerline') {
      const sw = Math.max(1, options.strokeWidth || 1);
      const plain = paths.map(p => `<path d="${p.d}" fill="none" stroke="${p.stroke || '#000'}" stroke-linecap="round" stroke-linejoin="round" stroke-width="${sw}"/>`).join('');
      return `${open}${plain}</svg>`;
    }
    if (options.groupByColor) {
      const byFill = new Map();
      for (const p of paths) {
        if (!byFill.has(p.fill)) byFill.set(p.fill, []);
        byFill.get(p.fill).push(p.d);
      }
      const groups = [];
      for (const [fill, ds] of byFill.entries()) {
        groups.push(`<g id="layer-${fill.replace(/[^\w#(),]/g,'')}">${ds.map(d=>`<path d="${d}" fill="${fill}" stroke="none"/>`).join('')}</g>`);
      }
      return `${open}${groups.join('')}</svg>`;
    }
    const plain = paths.map(p => `<path d="${p.d}" fill="${p.fill}" stroke="none"/>`).join('');
    return `${open}${plain}</svg>`;
  }

  function pathFromPoints(points, precision = 0) {
    if (!points.length) return '';
    const fmt = (n) => precision > 0 ? Number(n).toFixed(precision) : n;
    let d = `M ${fmt(points[0][0])} ${fmt(points[0][1])}`;
    for (let i = 1; i < points.length; i++) d += ` L ${fmt(points[i][0])} ${fmt(points[i][1])}`;
    d += ' Z';
    return d;
  }

  async function vectorize(file, options = {}, progressCb) {
    const { canvas, ctx, width, height } = await loadImageToCanvas(file);
    if ((options.blur || 0) > 0) gaussianBlur(ctx, width, height, options.blur);
    progressCb?.({ stage: 'load', progress: 10 });
    const img = getImageData(ctx, width, height);
    const k = Math.max(2, Math.min(16, options.colors || 8));
    const { labels, centroids } = kmeansQuantize(img.data, k, 8, options.palette);
    progressCb?.({ stage: 'quantize', progress: 40 });
    const labelImg = buildLabelImage(labels, width, height);
    const regionList = connectedComponents(labelImg, width, height);
    progressCb?.({ stage: 'regions', progress: 70 });
    let svgString = assembleSVG(regionList, centroids, width, height, options);
    if (options.optimizeSvg) {
      // Simple minify: remove extra spaces
      svgString = svgString.replace(/\s{2,}/g, ' ').trim();
    }
    progressCb?.({ stage: 'complete', progress: 100 });
    return { svgString, width, height };
  }

  window.vectorizeProcessor = { vectorize };
})();


