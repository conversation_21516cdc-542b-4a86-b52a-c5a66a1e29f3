// Vectorize Web Worker
// Receives { imageData, options } and returns { svgString, width, height }

function kmeansQuantize(pixels, k, maxIter = 8, palette) {
  const n = pixels.length / 4;
  const data = new Array(n);
  for (let i = 0; i < n; i++) {
    const idx = i * 4;
    data[i] = [pixels[idx], pixels[idx+1], pixels[idx+2]];
  }
  const centroids = [];
  if (palette && Array.isArray(palette) && palette.length) {
    for (let i = 0; i < Math.min(k, palette.length); i++) {
      centroids.push(palette[i].slice());
    }
  }
  const used = new Set();
  while (centroids.length < k) {
    const r = Math.floor(Math.random() * n);
    if (!used.has(r)) { centroids.push(data[r].slice()); used.add(r); }
  }
  const labels = new Uint16Array(n);
  for (let iter = 0; iter < maxIter; iter++) {
    for (let i = 0; i < n; i++) {
      let best = 0; let bestD = Infinity;
      const [r,g,b] = data[i];
      for (let c = 0; c < k; c++) {
        const cr = centroids[c][0] - r;
        const cg = centroids[c][1] - g;
        const cb = centroids[c][2] - b;
        const d = cr*cr + cg*cg + cb*cb;
        if (d < bestD) { bestD = d; best = c; }
      }
      labels[i] = best;
    }
    const sum = new Array(k).fill(0).map(()=>[0,0,0,0]);
    for (let i = 0; i < n; i++) {
      const l = labels[i];
      const [r,g,b] = data[i];
      sum[l][0]+=r; sum[l][1]+=g; sum[l][2]+=b; sum[l][3]++;
    }
    for (let c = 0; c < k; c++) {
      if (sum[c][3] > 0) {
        centroids[c][0] = Math.round(sum[c][0]/sum[c][3]);
        centroids[c][1] = Math.round(sum[c][1]/sum[c][3]);
        centroids[c][2] = Math.round(sum[c][2]/sum[c][3]);
      }
    }
  }
  return { labels, centroids };
}

function connectedComponents(labelImg, width, height) {
  const visited = new Uint8Array(width * height);
  const regions = [];
  const dirs = [[1,0],[-1,0],[0,1],[0,-1]];
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const i = y*width + x;
      if (visited[i]) continue;
      visited[i] = 1;
      const color = labelImg[i];
      const queue = [i];
      const pixels = [i];
      while (queue.length) {
        const q = queue.pop();
        const qx = q % width;
        const qy = (q - qx)/width;
        for (const [dx,dy] of dirs) {
          const nx = qx + dx, ny = qy + dy;
          if (nx<0||ny<0||nx>=width||ny>=height) continue;
          const ni = ny*width + nx;
          if (!visited[ni] && labelImg[ni] === color) {
            visited[ni] = 1;
            queue.push(ni);
            pixels.push(ni);
          }
        }
      }
      regions.push({ color, pixels });
    }
  }
  return regions;
}

function perpendicularDistance(p, a, b) {
  const [x,y] = p, [x1,y1] = a, [x2,y2] = b;
  const A = x - x1, B = y - y1, C = x2 - x1, D = y2 - y1;
  const dot = A*C + B*D;
  const len = C*C + D*D;
  let param = -1;
  if (len !== 0) param = dot / len;
  let xx, yy;
  if (param < 0) { xx = x1; yy = y1; }
  else if (param > 1) { xx = x2; yy = y2; }
  else { xx = x1 + param * C; yy = y1 + param * D; }
  const dx = x - xx, dy = y - yy;
  return Math.sqrt(dx*dx + dy*dy);
}

function rdpSimplify(points, epsilon) {
  if (points.length < 3) return points.slice();
  const dmax = { d: 0, idx: 0 };
  const start = points[0], end = points[points.length-1];
  for (let i = 1; i < points.length-1; i++) {
    const d = perpendicularDistance(points[i], start, end);
    if (d > dmax.d) { dmax.d = d; dmax.idx = i; }
  }
  if (dmax.d > epsilon) {
    const left = rdpSimplify(points.slice(0, dmax.idx+1), epsilon);
    const right = rdpSimplify(points.slice(dmax.idx), epsilon);
    return left.slice(0, -1).concat(right);
  } else {
    return [start, end];
  }
}

function traceContour(pixels, width, height) {
  const mask = new Uint8Array(width*height);
  for (const p of pixels) mask[p]=1;
  let start = -1;
  for (const p of pixels) {
    const x = p % width, y = (p-x)/width;
    if ((x>0 && !mask[p-1]) || (x<width-1 && !mask[p+1]) || (y>0 && !mask[p-width]) || (y<height-1 && !mask[p+width])) { start = p; break; }
  }
  if (start === -1) return [];
  const contour = [];
  let cx = start % width, cy = (start - cx)/width;
  let dir = 0;
  const neighbors = [[1,0],[1,1],[0,1],[-1,1],[-1,0],[-1,-1],[0,-1],[1,-1]];
  let guard = 0;
  do {
    contour.push([cx,cy]);
    let found = false;
    for (let k = 0; k < 8; k++) {
      const idx = (dir + k) % 8;
      const [dx,dy] = neighbors[idx];
      const nx = cx + dx, ny = cy + dy;
      if (nx<0||ny<0||nx>=width||ny>=height) continue;
      if (mask[ny*width + nx]) { cx = nx; cy = ny; dir = (idx + 6) % 8; found = true; break; }
    }
    if (!found) break;
    guard++;
    if (guard > pixels.length*4) break;
  } while (!(cx === start % width && cy === (start - (start%width))/width));
  return contour;
}

function assembleSVG(regions, palette, width, height, options) {
  let filtered = regions;
  if (options.removeBg && regions.length) {
    let maxArea = -1, maxIdx = -1;
    regions.forEach((r, i) => { if (r.pixels.length > maxArea) { maxArea = r.pixels.length; maxIdx = i; } });
    filtered = regions.filter((_, i) => i !== maxIdx);
  }
  filtered = filtered.filter(r => r.pixels.length >= (options.minArea || 0));
  const paths = [];
  if (options.tracingMode === 'centerline') {
    for (const r of filtered) {
      // Generate centerlines via skeletonization instead of just contour
      const skelPaths = skeletonizeRegionPaths(r.pixels, width, height, options.precision || 0);
      const color = palette[r.color];
      const stroke = `rgb(${color[0]},${color[1]},${color[2]})`;
      for (const d of skelPaths) paths.push({ d, stroke });
    }
  } else {
    for (const r of filtered) {
      const contour = traceContour(r.pixels, width, height);
      if (!contour.length) continue;
      const eps = Math.max(0.25, 1.5 * (4 - (options.detail || 3)));
      const simple = rdpSimplify(contour, eps);
      const d = pathFromPoints(simple, options.precision || 0);
      const color = palette[r.color];
      const fill = `rgb(${color[0]},${color[1]},${color[2]})`;
      paths.push({ d, fill });
    }
  }
  if (options.mergePaths && options.tracingMode !== 'centerline') {
    const byFill = new Map();
    for (const p of paths) {
      if (!byFill.has(p.fill)) byFill.set(p.fill, []);
      byFill.get(p.fill).push(p.d);
    }
    const merged = [];
    for (const [fill, ds] of byFill.entries()) {
      merged.push(`<path d="${ds.join(' ')}" fill="${fill}" stroke="none"/>`);
    }
    const open = options.responsiveSvg
      ? `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">`
      : `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
    let svg = `${open}${merged.join('')}</svg>`;
    if (options.optimizeSvg) svg = svg.replace(/\s{2,}/g, ' ').trim();
    return svg;
  }
  const open = options.responsiveSvg
    ? `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">`
    : `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
  if (options.tracingMode === 'outline') {
    let svg = `${open}${paths.map(p=>`<path d="${p.d}" fill="none" stroke="${p.fill}" stroke-width="1"/>`).join('')}</svg>`;
    if (options.optimizeSvg) svg = svg.replace(/\s{2,}/g, ' ').trim();
    return svg;
  }
  if (options.tracingMode === 'centerline') {
    const sw = Math.max(1, options.strokeWidth || 1);
    let svg = `${open}${paths.map(p=>`<path d="${p.d}" fill="none" stroke="${p.stroke || '#000'}" stroke-linecap="round" stroke-linejoin="round" stroke-width="${sw}"/>`).join('')}</svg>`;
    if (options.optimizeSvg) svg = svg.replace(/\s{2,}/g, ' ').trim();
    return svg;
  }
  // Optional no-overlap: subtract previously drawn colors (approx via order). TODO: real boolean ops
  if (options.noOverlap && options.tracingMode !== 'centerline') {
    // Note: This is a placeholder; full boolean union/subtract requires a path library.
    // We keep color order: earlier colors remain, later colors are drawn afterwards.
  }

  // Group by color option
  if (options.groupByColor) {
    const byFill = new Map();
    for (const p of paths) {
      if (!byFill.has(p.fill)) byFill.set(p.fill, []);
      byFill.get(p.fill).push(p.d);
    }
    const groups = [];
    for (const [fill, ds] of byFill.entries()) {
      groups.push(`<g id="layer-${fill.replace(/[^\w#(),]/g,'')}">${ds.map(d=>`<path d="${d}" fill="${fill}" stroke="none"/>`).join('')}</g>`);
    }
    let svg = `${open}${groups.join('')}</svg>`;
    if (options.optimizeSvg) svg = svg.replace(/\s{2,}/g, ' ').trim();
    return svg;
  }
  let svg = `${open}${paths.map(p=>`<path d="${p.d}" fill="${p.fill}" stroke="none"/>`).join('')}</svg>`;
  if (options.optimizeSvg) svg = svg.replace(/\s{2,}/g, ' ').trim();
  return svg;
}

function pathFromPoints(points, precision = 0) {
  if (!points.length) return '';
  const fmt = (n) => precision > 0 ? Number(n).toFixed(precision) : n;
  let d = `M ${fmt(points[0][0])} ${fmt(points[0][1])}`;
  for (let i = 1; i < points.length; i++) d += ` L ${fmt(points[i][0])} ${fmt(points[i][1])}`;
  return d + ' Z';
}

self.onmessage = (e) => {
  const { imageData, width, height, options } = e.data;
  try {
    const k = Math.max(2, Math.min(16, options.colors || 8));
    const { labels, centroids } = kmeansQuantize(imageData, k, 8, options.palette);
    // lightweight progress notifications (best-effort)
    self.postMessage({ ok: true, progress: 25 });
    const labelImg = new Uint16Array(width*height);
    labelImg.set(labels);
    self.postMessage({ ok: true, progress: 50 });
    const regions = connectedComponents(labelImg, width, height);
    self.postMessage({ ok: true, progress: 75 });
    const svgString = assembleSVG(regions, centroids, width, height, options);
    self.postMessage({ ok: true, result: { svgString, width, height } });
  } catch (err) {
    self.postMessage({ ok: false, error: err?.message || String(err) });
  }
};

// --- Zhang-Suen thinning (skeletonization) utilities (used in future improvements) ---
function zhangSuenThinning(binary, width, height) {
  // binary: Uint8Array 0/1 size width*height
  const img = new Uint8Array(binary); // copy
  let changed;
  const idx = (x,y) => y*width + x;
  const get = (x,y) => (x>=0 && y>=0 && x<width && y<height) ? img[idx(x,y)] : 0;
  do {
    changed = false;
    const toRemove = [];
    for (let y=1;y<height-1;y++) {
      for (let x=1;x<width-1;x++) {
        const p = idx(x,y);
        if (img[p]!==1) continue;
        const p2=get(x,y-1), p3=get(x+1,y-1), p4=get(x+1,y), p5=get(x+1,y+1), p6=get(x,y+1), p7=get(x-1,y+1), p8=get(x-1,y), p9=get(x-1,y-1);
        const neighbors = p2+p3+p4+p5+p6+p7+p8+p9;
        if (neighbors<2 || neighbors>6) continue;
        const transitions = ((p2===0 && p3===1)?1:0) + ((p3===0 && p4===1)?1:0) + ((p4===0 && p5===1)?1:0) + ((p5===0 && p6===1)?1:0) + ((p6===0 && p7===1)?1:0) + ((p7===0 && p8===1)?1:0) + ((p8===0 && p9===1)?1:0) + ((p9===0 && p2===1)?1:0);
        if (transitions!==1) continue;
        if (p2*p4*p6!==0) continue;
        if (p4*p6*p8!==0) continue;
        toRemove.push(p);
      }
    }
    if (toRemove.length>0) { changed=true; for (const p of toRemove) img[p]=0; }
    toRemove.length = 0;
    for (let y=1;y<height-1;y++) {
      for (let x=1;x<width-1;x++) {
        const p = idx(x,y);
        if (img[p]!==1) continue;
        const p2=get(x,y-1), p3=get(x+1,y-1), p4=get(x+1,y), p5=get(x+1,y+1), p6=get(x,y+1), p7=get(x-1,y+1), p8=get(x-1,y), p9=get(x-1,y-1);
        const neighbors = p2+p3+p4+p5+p6+p7+p8+p9;
        if (neighbors<2 || neighbors>6) continue;
        const transitions = ((p2===0 && p3===1)?1:0) + ((p3===0 && p4===1)?1:0) + ((p4===0 && p5===1)?1:0) + ((p5===0 && p6===1)?1:0) + ((p6===0 && p7===1)?1:0) + ((p7===0 && p8===1)?1:0) + ((p8===0 && p9===1)?1:0) + ((p9===0 && p2===1)?1:0);
        if (transitions!==1) continue;
        if (p2*p4*p8!==0) continue;
        if (p2*p6*p8!==0) continue;
        toRemove.push(p);
      }
    }
    if (toRemove.length>0) { changed=true; for (const p of toRemove) img[p]=0; }
  } while (changed);
  return img;
}

function traceSkeletonPaths(skeleton, width, height, precision) {
  const visited = new Uint8Array(width*height);
  const idx = (x,y)=>y*width+x;
  const get = (x,y)=>(x>=0&&y>=0&&x<width&&y<height) ? skeleton[idx(x,y)] : 0;
  const neighbors8 = [[1,0],[1,1],[0,1],[-1,1],[-1,0],[-1,-1],[0,-1],[1,-1]];
  const degree = (x,y)=>{
    let d=0; for (const [dx,dy] of neighbors8) if (get(x+dx,y+dy)) d++; return d;
  };
  const paths=[];
  const walkFrom=(sx,sy)=>{
    const path=[[sx,sy]]; visited[idx(sx,sy)]=1; let x=sx,y=sy;
    while(true){
      let next=null;
      for(const [dx,dy] of neighbors8){
        const nx=x+dx, ny=y+dy, ni=idx(nx,ny);
        if (get(nx,ny) && !visited[ni]){ next=[nx,ny]; break; }
      }
      if (!next) break;
      [x,y]=next; visited[idx(x,y)]=1; path.push([x,y]);
      if (degree(x,y)!==2) break;
    }
    if (path.length>1) paths.push(path);
  };
  // start from endpoints and junctions
  for (let y=0;y<height;y++) for (let x=0;x<width;x++) if (get(x,y)){
    const d=degree(x,y); const i=idx(x,y);
    if (!visited[i] && (d===1 || d>2)) walkFrom(x,y);
  }
  // trace remaining cycles
  for (let y=0;y<height;y++) for (let x=0;x<width;x++) if (get(x,y)){
    const i=idx(x,y); if (!visited[i]) walkFrom(x,y);
  }
  // convert to SVG path strings
  const fmt=(n)=> precision>0? Number(n).toFixed(precision): n;
  return paths.map(pts=>{
    let d=`M ${fmt(pts[0][0])} ${fmt(pts[0][1])}`;
    for (let i=1;i<pts.length;i++) d+=` L ${fmt(pts[i][0])} ${fmt(pts[i][1])}`;
    return d;
  });
}

function getRegionBounds(regionPixels, width) {
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
  for (const p of regionPixels) {
    const x = p % width; const y = (p - x) / width;
    if (x < minX) minX = x; if (y < minY) minY = y;
    if (x > maxX) maxX = x; if (y > maxY) maxY = y;
  }
  if (minX === Infinity) return null;
  return { minX, minY, maxX, maxY, w: maxX - minX + 1, h: maxY - minY + 1 };
}

function skeletonizeRegionPaths(regionPixels, width, height, precision) {
  const bounds = getRegionBounds(regionPixels, width);
  if (!bounds) return [];
  const { minX, minY, w, h } = bounds;
  const mask = new Uint8Array(w * h);
  for (const p of regionPixels) {
    const x = p % width; const y = (p - x) / width;
    const lx = x - minX; const ly = y - minY;
    mask[ly * w + lx] = 1;
  }
  const thin = zhangSuenThinning(mask, w, h);
  const paths = traceSkeletonPaths(thin, w, h, precision);
  // Offset back to full coords
  const offsetPaths = paths.map(d => d.replace(/([ML])\s*([\d\.\-]+)\s+([\d\.\-]+)/g, (m, cmd, xs, ys) => {
    const x = parseFloat(xs) + minX; const y = parseFloat(ys) + minY;
    return `${cmd} ${x} ${y}`;
  }));
  return offsetPaths;
}


