(function() {
  // Centralized dashboard mock/static data. All fields are optional.
  // Populate to override hardcoded strings in the rendered dashboard without editing component code.
  // This is used for both demo content and externalized static text.
  window.DashboardMockData = window.DashboardMockData || {
    fourCards: {
      currentMonth: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      lastMonth: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      currentYear: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      lastYear: { title: null, date: null, current: 0, previous: 0, hideComparison: true }
    },
    lastWeek: { title: null, date: null },
    todayVsPreviousYears: { date: null },
    monthlySales: { date: null },
    yearlySales: { date: null },
    lifetimeInsights: {
      // If provided, these override the top numbers via runtime text replacement
      // Demo defaults moved here so component stays free of inline mock values
      salesCount: 217223,
      royalties: '$511,933.0',
      returned: '(-17,099)',
      cancelled: '2,231',
      ads: '65,000'
    }
  };
})();


