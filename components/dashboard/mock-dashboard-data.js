(function() {
  // Centralized dashboard mock/static data. All fields are optional.
  // Populate to override hardcoded strings in the rendered dashboard without editing component code.
  // This is used for both demo content and externalized static text.
  window.DashboardMockData = window.DashboardMockData || {
    fourCards: {
      currentMonth: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      lastMonth: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      currentYear: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      lastYear: { title: null, date: null, current: 0, previous: 0, hideComparison: true }
    },
    lastWeek: { title: null, date: null },
    todayVsPreviousYears: { date: null },
    monthlySales: { date: null },
    yearlySales: { date: null },
    lifetimeInsights: {
      // If provided, these override the top numbers via runtime text replacement
      // Demo defaults moved here so component stays free of inline mock values
      salesCount: 217223,
      royalties: '$511,933.0',
      returned: '(-17,099)',
      cancelled: '2,231',
      ads: '65,000'
    }
    ,
    // Listings Status Overview mock numbers per marketplace.
    // Keys: 'all', 'us', 'uk', 'de', 'fr', 'it', 'es', 'jp'
    // Values: live, draft, auto, review, processing, translating, locked, timeout, rejected, silentRemovals
    listingsStatus: {
      all:      { live: 1233432, draft: 12, auto: 12432, review: 0, processing: 0, translating: 3, locked: 132, timeout: 132, rejected: 43, silentRemovals: 54 },
      us:       { live: 843211, draft: 5,  auto: 8032,  review: 0, processing: 0, translating: 2, locked: 71,  timeout: 80,  rejected: 21, silentRemovals: 23 },
      uk:       { live: 142233, draft: 2,  auto: 1500,  review: 0, processing: 0, translating: 0, locked: 10,  timeout: 8,   rejected: 6,  silentRemovals: 9 },
      de:       { live: 156701, draft: 1,  auto: 1432,  review: 0, processing: 0, translating: 0, locked: 12,  timeout: 10,  rejected: 5,  silentRemovals: 7 },
      fr:       { live: 111090, draft: 1,  auto: 1100,  review: 0, processing: 0, translating: 0, locked: 9,   timeout: 7,   rejected: 4,  silentRemovals: 6 },
      it:       { live: 95542,  draft: 1,  auto: 900,   review: 0, processing: 0, translating: 0, locked: 8,   timeout: 7,   rejected: 3,  silentRemovals: 4 },
      es:       { live: 101223, draft: 1,  auto: 968,   review: 0, processing: 0, translating: 1, locked: 7,   timeout: 6,   rejected: 3,  silentRemovals: 3 },
      jp:       { live: 82132,  draft: 1,  auto: 500,   review: 0, processing: 0, translating: 0, locked: 5,   timeout: 4,   rejected: 0,  silentRemovals: 0 }
    }
  };
})();


