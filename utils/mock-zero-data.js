(function() {
  // Global toggle: set to true to enable zero-data mock mode
  if (typeof window.USE_MOCK_ZERO_DATA === 'undefined') {
    window.USE_MOCK_ZERO_DATA = true;
  }

  const MockZeroData = {
    isEnabled() {
      return !!window.USE_MOCK_ZERO_DATA;
    },

    getZeroTodayVsPreviousYearsData() {
      // Return empty dataset; charts should handle gracefully
      return [];
    },

    getZeroMonthlySalesData(year, monthsToShow) {
      const months = Number.isFinite(monthsToShow) ? monthsToShow : 12;
      return Array.from({ length: months }, (_, i) => ({
        monthIndex: i,
        month: i + 1,
        sales: 0,
        royalties: 0,
        returns: 0,
        values: [0],
        labels: ['ALL']
      }));
    },

    getZeroLastWeekData() {
      const now = new Date();
      const data = [];
      const codes = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
      for (let i = 6; i >= 0; i--) {
        const d = new Date(now);
        d.setDate(now.getDate() - i);
        const marketplaces = codes.map(code => ({ code, sales: 0, royalties: 0, returns: 0 }));
        data.push({
          month: d.toLocaleString('en-US', { month: 'short' }).toUpperCase(),
          day: String(d.getDate()).padStart(2, '0'),
          year: d.getFullYear().toString().slice(-2),
          marketplaces,
          sales: 0,
          royalties: 0,
          returns: 0,
          values: marketplaces.map(() => 0),
          labels: marketplaces.map(m => m.code)
        });
      }
      return data;
    },

    applyDashboardZeroState() {
      try {
        const root = document.querySelector('.dashboard-component');
        if (!root) return;

        // Tier value
        const tierValue = root.querySelector('.tier-value');
        if (tierValue) tierValue.textContent = '10';

        // Metric subtexts 'X of Y' => '0 of 0' (no hardcoded totals)
        root.querySelectorAll('.metric-subtext').forEach(el => {
          el.innerHTML = '0 of 0<span class="metric-remaining"></span>';
        });

        // Progress bars to 0%
        root.querySelectorAll('.progress-fill').forEach(el => { el.style.width = '0%'; });

        // Listings status numbers
        const liveNum = document.getElementById('live-listings-count');
        if (liveNum) { liveNum.textContent = '0'; liveNum.classList.remove('live'); liveNum.classList.add('zero'); }
        root.querySelectorAll('.listing-status-number').forEach(el => { el.textContent = '0'; });

        // Ad spend header values and orders
        root.querySelectorAll('.ad-spend-header-value').forEach(el => {
          const t = (el.textContent || '').trim();
          const symbol = t.charAt(0);
          el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
        });
        root.querySelectorAll('.ad-spend-orders').forEach(el => { el.textContent = '(0)'; });

        // Ad spend marketplace columns
        root.querySelectorAll('.ad-spend-marketplace-col .ad-spend-value').forEach(el => {
          const t = (el.textContent || '').trim();
          const symbol = t.charAt(0);
          el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
        });
        root.querySelectorAll('.ad-spend-marketplace-col .ad-spend-acos-value').forEach(el => { el.textContent = '0.0'; });

        // Sales cards and marketplace totals
        document.querySelectorAll('.todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div').forEach(card => {
          const salesCount = card.querySelector('.sales-count');
          if (salesCount) salesCount.textContent = '0';

          // Zero inner metrics (royalties, returned, cancelled, new, ads)
          const royaltiesEl = card.querySelector('.metric-col.royalties-metric .metric-value');
          if (royaltiesEl) {
            const t = (royaltiesEl.textContent || '').trim();
            const symbol = t.charAt(0);
            royaltiesEl.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
          }
          ['returned','cancelled','new','ads'].forEach(cls => {
            const el = card.querySelector(`.metric-col.${cls}-metric .metric-value`);
            if (el) el.textContent = '0';
            const per = card.querySelector(`.metric-col.${cls}-metric .metric-percentage`);
            if (per) per.textContent = '0.0%';
          });

          if (window.updateNoSalesStateForCard) window.updateNoSalesStateForCard(card, 0);
          if (window.updateAllPercentageCalculations) window.updateAllPercentageCalculations(card, 'all');
        });
        root.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; });
        root.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
          const t = (el.textContent || '').trim();
          const symbol = t.charAt(0);
          el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
        });
        root.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; });

        // Four-sales-cards section (Current/Last Month/Year cards)
        document.querySelectorAll('.four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div').forEach(card => {
          const salesCount = card.querySelector('.sales-count');
          if (salesCount) salesCount.textContent = '0';
          const royaltiesEl = card.querySelector('.metric-col.royalties-metric .metric-value');
          if (royaltiesEl) {
            const t = (royaltiesEl.textContent || '').trim();
            const symbol = t.charAt(0);
            royaltiesEl.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
          }
          ['returned','cancelled','new','ads'].forEach(cls => {
            const el = card.querySelector(`.metric-col.${cls}-metric .metric-value`);
            if (el) el.textContent = '0';
            const per = card.querySelector(`.metric-col.${cls}-metric .metric-percentage`);
            if (per) per.textContent = '0.0%';
          });
          card.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; });
          card.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
          });
          card.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; });
        });

        // Top-four-sales-cards section (Top Day/Week/Month/Year)
        document.querySelectorAll('.top-four-sales-cards-section .top-day-card-div, .top-four-sales-cards-section .top-week-card-div, .top-four-sales-cards-section .top-month-card-div, .top-four-sales-cards-section .top-year-card-div').forEach(card => {
          const salesCount = card.querySelector('.sales-count');
          if (salesCount) salesCount.textContent = '0';
          const royaltiesEl = card.querySelector('.metric-col.royalties-metric .metric-value');
          if (royaltiesEl) {
            const t = (royaltiesEl.textContent || '').trim();
            const symbol = t.charAt(0);
            royaltiesEl.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
          }
          ['returned','cancelled','new','ads'].forEach(cls => {
            const el = card.querySelector(`.metric-col.${cls}-metric .metric-value`);
            if (el) el.textContent = '0';
            const per = card.querySelector(`.metric-col.${cls}-metric .metric-percentage`);
            if (per) per.textContent = '0.0%';
          });
          card.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; });
          card.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
          });
          card.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; });
        });

        // Customer Reviews card zeros
        const reviewsCard = root.querySelector('.customer-reviews-card-div');
        if (reviewsCard) {
          const totalReviews = reviewsCard.querySelector('.reviews-count');
          if (totalReviews) totalReviews.textContent = '0';
          // Comments and Average Rating
          const metrics = reviewsCard.querySelectorAll('.comments-ratings-div .metric-value');
          metrics.forEach((el, idx) => { el.textContent = idx === 1 ? '0.0' : '0'; });
          // Marketplace review counts
          reviewsCard.querySelectorAll('.marketplace-total-reviews-count').forEach(el => { el.textContent = '0'; });
          // Remove reviews list items
          const list = reviewsCard.querySelector('.reviews-list');
          if (list) list.innerHTML = '';
        }

        // Lifetime Insights zeros
        const lifetimeCard = root.querySelector('.lifetime-insights-card-div');
        if (lifetimeCard) {
          lifetimeCard.querySelectorAll('.lifetime-data-div .lifetime-data-item .lifetime-data-value').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            // If looks like currency
            if (['$','£','€','¥','-'].includes(symbol)) {
              // Keep negative sign if present
              if (symbol === '-') {
                const next = t.charAt(1);
                const cur = ['$','£','€','¥'].includes(next) ? next : '$';
                el.textContent = `-${cur === '£' ? '£0.00' : cur === '€' ? '€0.00' : cur === '¥' ? '¥0' : '$0.00'}`;
              } else {
                el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
              }
            } else {
              el.textContent = '0';
            }
          });
          const taxRate = lifetimeCard.querySelector('.lifetime-tax-rate');
          if (taxRate) taxRate.textContent = '0%';

          // Zero the top sales count in Lifetime Insights
          const lifetimeSalesCount = lifetimeCard.querySelector('.sales-count');
          if (lifetimeSalesCount) lifetimeSalesCount.textContent = '0';

          // Zero analytics metrics (Royalties, Returned, Cancelled, Ads) in Lifetime Insights
          const lifetimeRoyalties = lifetimeCard.querySelector('.metric-col.royalties-metric .metric-value');
          if (lifetimeRoyalties) {
            const txt = (lifetimeRoyalties.textContent || '').trim();
            const sym = txt.charAt(0);
            lifetimeRoyalties.textContent = sym === '£' ? '£0.00' : sym === '€' ? '€0.00' : sym === '¥' ? '¥0' : '$0.00';
          }
          const lifetimeReturnedValue = lifetimeCard.querySelector('.metric-col.returned-metric .metric-value');
          if (lifetimeReturnedValue) lifetimeReturnedValue.textContent = '0';
          const lifetimeReturnedPer = lifetimeCard.querySelector('.metric-col.returned-metric .metric-percentage');
          if (lifetimeReturnedPer) lifetimeReturnedPer.textContent = '0.0%';

          const lifetimeCancelled = lifetimeCard.querySelector('.metric-col.cancelled-metric .metric-value');
          if (lifetimeCancelled) lifetimeCancelled.textContent = '0';

          const lifetimeAdsValue = lifetimeCard.querySelector('.metric-col.ads-metric .metric-value');
          if (lifetimeAdsValue) lifetimeAdsValue.textContent = '0';
          const lifetimeAdsPer = lifetimeCard.querySelector('.metric-col.ads-metric .metric-percentage');
          if (lifetimeAdsPer) lifetimeAdsPer.textContent = '0.0%';

          // Zero marketplaces totals inside Lifetime Insights card
          lifetimeCard.querySelectorAll('.marketplace-total-sales-count').forEach(el => { el.textContent = '0'; });
          lifetimeCard.querySelectorAll('.marketplace-total-earned-royalties').forEach(el => {
            const t = (el.textContent || '').trim();
            const symbol = t.charAt(0);
            el.textContent = symbol === '£' ? '£0.00' : symbol === '€' ? '€0.00' : symbol === '¥' ? '¥0' : '$0.00';
          });
          lifetimeCard.querySelectorAll('.marketplace-total-returned-units').forEach(el => { el.textContent = '(0)'; });
        }
      } catch (e) {
        console.warn('MockZeroData.applyDashboardZeroState failed', e);
      }
    },

    disableRealtimeIfNeeded() {
      if (!this.isEnabled()) return;
      try {
        if (window.stopRealTimeUpdates) window.stopRealTimeUpdates();
        window.startRealTimeUpdates = () => {
          if (window.SnapLogger) window.SnapLogger.info('🧪 Mock Zero Data: real-time updates disabled');
        };
      } catch (e) {
        console.warn('MockZeroData.disableRealtimeIfNeeded failed', e);
      }
    }
  };

  window.MockZeroData = MockZeroData;

  // Apply immediate runtime effects if enabled
  if (MockZeroData.isEnabled()) {
    MockZeroData.disableRealtimeIfNeeded();
  }

  // React when dashboard initializes
  window.addEventListener('dashboard:initialized', () => {
    if (MockZeroData.isEnabled()) MockZeroData.applyDashboardZeroState();
  });
})();


