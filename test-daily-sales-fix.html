<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Sales Chart Fix Test</title>
    <link rel="stylesheet" href="assets/css/snap-charts.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #fff;
        }
        .test-description {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 20px;
        }
        .chart-container {
            height: 400px;
            background: #333;
            border-radius: 6px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Daily Sales Chart Fix Test</h1>
        <p>Testing the fixes for column alignment and tooltip functionality in daily sales history charts.</p>
        
        <div class="test-section">
            <div class="test-title">Daily Sales History Chart - Column Alignment & Tooltip Test</div>
            <div class="test-description">
                This chart should show:
                <br>• Columns aligned with the bottom grid line (baseline)
                <br>• Working tooltips when hovering over columns
                <br>• Proper canvas layer positioning
            </div>
            <div class="chart-container" id="daily-sales-test"></div>
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Generate test data for daily sales history
        function generateDailySalesData() {
            const data = [];
            const startDate = new Date('2024-12-01');
            
            for (let i = 0; i < 31; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);
                
                const sales = Math.floor(Math.random() * 50) + 5;
                const royalties = Math.floor(sales * 0.15);
                const returns = Math.floor(Math.random() * 5);
                
                data.push({
                    month: date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
                    day: date.getDate().toString().padStart(2, '0'),
                    year: date.getFullYear().toString().slice(-2),
                    sales: sales,
                    royalties: royalties,
                    returns: returns,
                    dateObj: new Date(date)
                });
            }
            
            return data;
        }

        // Create the test chart
        const testData = generateDailySalesData();
        
        const dailySalesChart = new SnapChart({
            container: '#daily-sales-test',
            type: 'daily-sales-history',
            data: testData,
            options: {
                title: 'Daily Sales History Test',
                subtitle: 'December 2024',
                showReturns: true,
                showRoyalties: true,
                currentStartDate: new Date('2024-12-01'),
                currentEndDate: new Date('2024-12-31'),
                allTimeData: testData
            },
            demoOptions: {
                showContainer: true,
                showTitle: true,
                showDataEditor: false,
                showControls: false,
                showInsights: true
            }
        });

        // Add some debugging info
        console.log('Daily Sales Chart created:', dailySalesChart);
        console.log('Test data:', testData);
        
        // Test tooltip functionality after a short delay
        setTimeout(() => {
            console.log('Testing tooltip functionality...');
            const hoverAreas = document.querySelectorAll('.snap-chart-column-hover-area');
            console.log('Found hover areas:', hoverAreas.length);

            if (hoverAreas.length > 0) {
                console.log('✅ Hover areas created successfully');

                // Test hover area positioning
                const firstHoverArea = hoverAreas[0];
                const hoverRect = firstHoverArea.getBoundingClientRect();
                console.log('First hover area position:', {
                    x: firstHoverArea.getAttribute('x'),
                    y: firstHoverArea.getAttribute('y'),
                    width: firstHoverArea.getAttribute('width'),
                    height: firstHoverArea.getAttribute('height'),
                    boundingRect: hoverRect
                });

                // Check canvas positioning
                const canvas = document.querySelector('.snap-chart-dsh-layer');
                if (canvas) {
                    const canvasRect = canvas.getBoundingClientRect();
                    console.log('Canvas position:', {
                        left: canvas.style.left,
                        top: canvas.style.top,
                        width: canvas.style.width,
                        height: canvas.style.height,
                        boundingRect: canvasRect
                    });
                }
            } else {
                console.log('❌ No hover areas found - tooltip functionality may be broken');
            }
        }, 1000);
    </script>
</body>
</html>
