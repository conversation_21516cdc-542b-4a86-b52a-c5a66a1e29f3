# Current Task: Reset Lifetime card zero/gray state when switching marketplace focus

## Context7 + Sequential Thinking
- Context: After viewing a marketplace with zero sales (e.g., JP) the Lifetime card gets gray `zero` classes applied. When switching to another focus (e.g., All Marketplaces) where values are positive/negative, the card remains gray due to lingering classes.
- Intent: On every marketplace toggle, clear prior zero-state styling on the Lifetime card and then re-apply correct styling per the new focus.
- Dependencies: `applyMarketplaceFocus`, `updateLifetimeInsightsForMarketplace`, `enforceZeroStateForLifetimeCard`, CSS zero styles.

## Discovery Documentation (Search Phase)
- Relevant files: `components/dashboard/dashboard.js`, `snapapp.css`.
- DOM generation: Lifetime card built via HTML template in `dashboard.js`.
- CSS: `.lifetime-data-value.zero`, `.metric-value.*.negative/positive` control colors.
- Chrome APIs: N/A.
- Event binding: Marketplace dropdown triggers `applyMarketplaceFocus()`.

## Existing Functionality
- Date placeholders were applied to all `.sales-card-date` when `hasData` is false.
- Insights button disabled/enabled based on data.

## Gaps Identified
- No helper existed to clear zero-state on Lifetime card before re-hydrating.

## Proposed Approach
- Add `clearZeroStateForLifetimeCard(card)` to remove lingering classes and re-enable controls.
- Call it at the start of `applyMarketplaceFocus()` batch before `updateLifetimeInsightsForMarketplace()`.

## Tasks
- [x] Implement `clearZeroStateForLifetimeCard(card)`.
- [x] Invoke it during `applyMarketplaceFocus()` before re-hydration.
- [x] Keep `updateLifetimeInsightsForMarketplace(selectedMarketplace)` call in focus flow.
- [ ] Test JP -> All Marketplaces transition to ensure colors update.

## Implementation Tracking
- HTML generation: unchanged.
- CSS injection: unchanged.
- Chrome API usage: none.
- Dynamic content: Lifetime card values updated via `updateLifetimeInsightsForMarketplace`.

## Testing Strategy
- Switch to JP (with zeros) then to All Marketplaces; verify Lifetime metrics show green/red where appropriate, no lingering gray.
- Switch between multiple single marketplaces and back to All; verify consistency.

## Linter & Error Verification
- Run lints on `components/dashboard/dashboard.js` and verify no errors.

## Notes
- This avoids UI confusion on daily cards while keeping top-four no-data messaging.
