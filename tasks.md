# Current Task: Include `lifetime-insights-card-div` in mock zero-data handling

## Context7 + Sequential Thinking MCP
- Context: The dashboard UI is rendered by `components/dashboard/dashboard.js` and loaded via `snapapp.js`. The page loads `utils/mock-zero-data.js` early from `index.html`, which provides a zero-data mode controlled by `window.USE_MOCK_ZERO_DATA`. The Lifetime Insights card structure is defined in `components/dashboard/dashboard.js` under `.lifetime-insights-card-div`. No `manifest.json` is present in the project; this runs as a web app but is structured to be extension-compatible. No Chrome APIs are used.
- Intent: Ensure zero-data mode also resets the Lifetime Insights card top sales count, analytics metrics, and marketplace totals, in addition to the existing lifetime summary rows.
- Dependencies: Zero-data hook listens for `dashboard:initialized` and then mutates DOM within `.dashboard-component`. Styles for the Lifetime Insights card live in `snapapp.css` and should gracefully handle zeros.

## Discovery Documentation (Search Phase)
- Existing Files Found:
  - `utils/mock-zero-data.js` (zero-data helpers; already resetting many cards)
  - `components/dashboard/dashboard.js` (defines `.lifetime-insights-card-div` markup)
  - `index.html` (loads `utils/mock-zero-data.js` before main scripts)
- Manifest/Permissions: No `manifest.json` found; no Chrome APIs like `chrome.storage` used.
- DOM generation methods: Combination of template strings (`innerHTML = ...`) and `document.createElement` across components.
- CSS injection: Some components inject `<style>` tags (e.g., charts, snap-image-studio). Global CSS via `snapapp.css`.
- Event binding: Direct event listeners added in component initializers; global custom events (`dashboard:initialized`).

## Project Patterns
- JS component organization: Single-file component modules with embedded HTML/CSS and init functions.
- HTML generation: Mostly string templates assigned via `innerHTML` plus targeted DOM updates.
- CSS-in-JS: Injected `<style>` for component-local styles; otherwise external CSS.
- State management: Global objects on `window` (e.g., `MockZeroData`, `DashboardMockData`).

## Gaps Identified
- Lifetime Insights card only zeroed the lifetime summary rows and tax rate; top sales count, analytics metrics, and marketplaces were not explicitly zeroed.
- No additional gaps for this task.

## Proposed Approach
- Enhance existing `applyDashboardZeroState()` in `utils/mock-zero-data.js` to:
  - Set `.lifetime-insights-card-div .sales-count` to `0`.
  - Zero metrics in `.analytics-div` (royalties to currency `0.00` with local symbol; returned/cancelled/ads to `0` and percentages to `0.0%`).
  - Zero all `.marketplace-total-*` within the Lifetime Insights card.
- No new files; no manifest changes.

## Tasks
- [x] Update `utils/mock-zero-data.js` to include `.lifetime-insights-card-div` zeroing (sales count, metrics, marketplaces).
- [x] Run linter on the changed file and verify no errors.
- [ ] Manual test: Load dashboard with `window.USE_MOCK_ZERO_DATA = true` and confirm Lifetime Insights renders all zeros consistently.

## Implementation Tracking
- HTML generation in JS: Unchanged; only DOM text mutations on render.
- CSS injection: Unchanged.
- Chrome API usage and permissions: None.
- Dynamic content: Uses DOM queries scoped to `.lifetime-insights-card-div`.

## Testing Strategy
- Open `index.html`, ensure `USE_MOCK_ZERO_DATA` is true (default in `mock-zero-data.js`).
- Verify in both themes that zeros render correctly and that no negative/parenthesized values appear outside marketplaces `(0)`.
- Use DevTools to watch for layout shifts; ensure no console warnings beyond known logs.

## Linter & Error Verification
- ESLint: Ran on `utils/mock-zero-data.js` (no errors reported).
- HTML/CSS: Visual check in browser for proper styling and no broken layout.

## Code Documentation
- The zeroing logic is contained within `applyDashboardZeroState()` under the Lifetime Insights section and mirrors patterns used for other cards.

## Code Quality (Spaghetti Review)
- Change is contained, scoped to one function, and follows existing modular pattern; no entanglement detected.
