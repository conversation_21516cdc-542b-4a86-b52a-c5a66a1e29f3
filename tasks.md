# Current Task: Snap Image Studio Tabs – Active state to #470CED with white icon/text (both themes)

## Context7 + Sequential Thinking
- Context: Snap Image Studio renders its own HTML/CSS/JS from `components/snap-image-studio/snap-image-studio.js`, injecting a CSS string (`snapImageStudioCSS`) and building `.tab` UI with icons and labels. Theme is controlled via CSS variables with `:root` (light) and `[data-theme="dark"]` overrides. Active state uses `--tab-active-bg` for background and `--tab-text-active` for text.
- Intent: Make the active tab background `#470CED` in both light and dark themes, and ensure active tab icon and text are white in both themes.
- Dependencies: Icon swapping via `updateTabIcons()` toggles between `data-active`/`data-inactive` SVGs. Existing dark-mode rule uses a filter to make active icons white only in dark mode.

## Discovery Documentation (Search Phase)
- Relevant files: `components/snap-image-studio/snap-image-studio.js` (CSS variables, tab styles, tab logic); `snapapp.css` (global tab styles, not used here); `components/charts/snap-charts.css` (separate tab system).
- DOM generation: Component uses template strings to inject HTML; tab switching adds/removes `.active` via `initializeTabs()`.
- CSS injection: Inline `<style>` tag injected with `snapImageStudioCSS`.
- Chrome APIs: Not applicable in this component.
- Event binding: Direct event listeners on `.tab` elements.

## Existing Functionality
- Light theme: `--tab-active-bg: #FFFFFF`, `--tab-text-active: #470CED`.
- Dark theme: `--tab-active-bg: #292E38`, `--tab-text-active: #FFFFFF`.
- Icon color: `[data-theme="dark"] .tab.active .tab-icon` applies white filter in dark mode only.

## Gaps Identified
- Active background not `#470CED`.
- Active text not white in light theme.
- Active icon not forced white in light theme.

## Proposed Approach
- Enhance existing CSS variables in `snap-image-studio.js`:
  - Set `--tab-active-bg: #470CED` for both `:root` and `[data-theme="dark"]`.
  - Set `--tab-text-active: #FFFFFF` in `:root` (already white in dark theme).
- Make the active icon white in both themes by changing `.tab.active .tab-icon` filter rule to be theme-agnostic.
- Keep hover/background container vars unchanged.

## Tasks
- [x] Update `:root` variables: `--tab-active-bg` to `#470CED`, `--tab-text-active` to `#FFFFFF`.
- [x] Update `[data-theme="dark"]` variable: `--tab-active-bg` to `#470CED`.
- [x] Apply white icon filter for `.tab.active .tab-icon` in both themes.
- [x] Set `--tab-container-bg` and `--drag-drop-bg` to `var(--bg-primary)` in both themes for Snap Image Studio.
- [ ] Verify UI in both light and dark themes.

## Implementation Tracking
- HTML generation: unchanged.
- CSS injection: updated variables and selector in `snap-image-studio.js`.
- Chrome API usage: none.

## Testing Strategy
- Toggle theme and switch between tabs; confirm active background `#470CED`, label text white, icon white in both themes.
- Verify sliding background inherits `#470CED`.

## Linter & Error Verification
- Run in browser; ensure no console errors and HTML structure remains valid.

## Notes
- Icon assets still swap on active/inactive; filter ensures appearance is white regardless of source color.
